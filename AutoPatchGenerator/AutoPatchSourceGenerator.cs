// AutoPatchSourceGenerator.cs - Instant generation with comprehensive validation
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Text;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Text;
using System;
using System.Threading;

[Generator]
public class AutoPatchSourceGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // Create a provider that watches for methods with AutoPatch attributes
        var autoPatchMethods = context.SyntaxProvider
            .CreateSyntaxProvider(
                predicate: static (s, _) => IsPotentialAutoPatchMethod(s),
                transform: static (ctx, _) => GetAutoPatchMethodInfo(ctx))
            .Where(static m => m is not null);

        // Combine with compilation for semantic analysis
        var compilationAndMethods = context.CompilationProvider.Combine(autoPatchMethods.Collect());

        // Register the source output
        context.RegisterSourceOutput(compilationAndMethods, static (spc, source) => Execute(spc, source.Left, source.Right));
    }

    private static bool IsPotentialAutoPatchMethod(SyntaxNode node)
    {
        return node is MethodDeclarationSyntax method &&
               method.AttributeLists.Count > 0 &&
               method.AttributeLists
                   .SelectMany(al => al.Attributes)
                   .Any(attr => attr.Name.ToString().Contains("AutoPatch"));
    }

    private static MethodDeclarationSyntax? GetAutoPatchMethodInfo(GeneratorSyntaxContext context)
    {
        var method = (MethodDeclarationSyntax)context.Node;
        var semanticModel = context.SemanticModel;

        // Verify this is actually an AutoPatch method
        var hasAutoPatchAttribute = method.AttributeLists
            .SelectMany(al => al.Attributes)
            .Any(attr =>
            {
                var symbolInfo = semanticModel.GetSymbolInfo(attr);
                return symbolInfo.Symbol?.ContainingType?.Name == "AutoPatchAttribute";
            });

        return hasAutoPatchAttribute ? method : null;
    }

    private static void Execute(SourceProductionContext context, Compilation compilation, ImmutableArray<MethodDeclarationSyntax?> methods)
    {
        if (methods.IsDefaultOrEmpty) return;

        var validPatches = new List<PatchDefinition>();
        var validator = new ComprehensiveValidator(compilation);

        foreach (var method in methods)
        {
            if (method == null) continue;

            var semanticModel = compilation.GetSemanticModel(method.SyntaxTree);
            var methodSymbol = semanticModel.GetDeclaredSymbol(method);

            if (methodSymbol == null) continue;

            var patchDef = AnalyzePatchMethod(methodSymbol, method, validator);

            if (patchDef.IsValid)
            {
                validPatches.Add(patchDef);

                // Report success with detailed info
                ReportDiagnostic(context, method, "AP001", "AutoPatch Success",
                    $"✓ Generated patch: {patchDef.OriginalMethodName} -> {patchDef.TargetSignature}",
                    DiagnosticSeverity.Info);
            }
            else
            {
                // Report detailed validation errors
                ReportDiagnostic(context, method, "AP002", "AutoPatch Validation Error",
                    patchDef.ErrorMessage, DiagnosticSeverity.Error);
            }
        }

        if (validPatches.Any())
        {
            // Generate the actual Harmony patches
            var harmonyPatchSource = GenerateHarmonyPatches(validPatches);
            context.AddSource("AutoPatches.g.cs", SourceText.From(harmonyPatchSource, Encoding.UTF8));

            // Generate patch applier
            var applierSource = GeneratePatchApplier(validPatches);
            context.AddSource("AutoPatchApplier.g.cs", SourceText.From(applierSource, Encoding.UTF8));
        }
    }

    // Comprehensive validation system for instant feedback
    public class ComprehensiveValidator
    {
        private readonly Compilation _compilation;
        private readonly Dictionary<string, INamedTypeSymbol> _typeCache = new();

        public ComprehensiveValidator(Compilation compilation)
        {
            _compilation = compilation;
        }

        public ValidationResult ValidateTargetMethod(string targetType, string targetMethod, IMethodSymbol patchMethod)
        {
            var result = new ValidationResult();

            // 1. Validate target type exists
            var targetTypeSymbol = GetTypeSymbol(targetType);
            if (targetTypeSymbol == null)
            {
                result.AddError($"Target type '{targetType}' not found. Check namespace and assembly references.");
                return result;
            }

            // 2. Find target method(s) - handle overloads
            var targetMethods = targetTypeSymbol.GetMembers(targetMethod).OfType<IMethodSymbol>().ToList();
            if (!targetMethods.Any())
            {
                result.AddError($"Target method '{targetMethod}' not found in type '{targetType}'. Available methods: {string.Join(", ", targetTypeSymbol.GetMembers().OfType<IMethodSymbol>().Select(m => m.Name).Distinct())}");
                return result;
            }

            // 3. Validate method signature compatibility
            var compatibleMethod = FindCompatibleTargetMethod(targetMethods, patchMethod);
            if (compatibleMethod == null)
            {
                result.AddError($"No compatible overload found for '{targetMethod}'. Available overloads:\n{string.Join("\n", targetMethods.Select(FormatMethodSignature))}");
                return result;
            }

            // 4. Validate parameter compatibility
            ValidateParameterCompatibility(compatibleMethod, patchMethod, result);

            // 5. Validate return type compatibility
            ValidateReturnTypeCompatibility(compatibleMethod, patchMethod, result);

            // 6. Validate access modifiers
            ValidateAccessibility(compatibleMethod, result);

            result.TargetMethod = compatibleMethod;
            return result;
        }

        private INamedTypeSymbol? GetTypeSymbol(string typeName)
        {
            if (_typeCache.TryGetValue(typeName, out var cached))
                return cached;

            var symbol = _compilation.GetTypeByMetadataName(typeName) ??
                        _compilation.Assembly.GlobalNamespace.GetTypeMembers()
                            .FirstOrDefault(t => t.ToDisplayString() == typeName);

            if (symbol != null)
                _typeCache[typeName] = symbol;

            return symbol;
        }

        private IMethodSymbol? FindCompatibleTargetMethod(List<IMethodSymbol> targetMethods, IMethodSymbol patchMethod)
        {
            // Try to find exact parameter match first, then compatible matches
            foreach (var targetMethod in targetMethods)
            {
                if (IsParameterCompatible(targetMethod, patchMethod))
                    return targetMethod;
            }
            return null;
        }

        private bool IsParameterCompatible(IMethodSymbol targetMethod, IMethodSymbol patchMethod)
        {
            var patchParams = patchMethod.Parameters.Where(p => !IsHarmonyParameter(p.Name)).ToList();
            var targetParams = targetMethod.Parameters.ToList();

            // Basic count check (allowing for Harmony parameters)
            if (patchParams.Count > targetParams.Count + 1) // +1 for __instance
                return false;

            // Check parameter types match
            for (int i = 0; i < Math.Min(patchParams.Count, targetParams.Count); i++)
            {
                if (!SymbolEqualityComparer.Default.Equals(patchParams[i].Type, targetParams[i].Type))
                    return false;
            }

            return true;
        }

        private void ValidateParameterCompatibility(IMethodSymbol targetMethod, IMethodSymbol patchMethod, ValidationResult result)
        {
            var patchParams = patchMethod.Parameters.ToList();
            var targetParams = targetMethod.Parameters.ToList();
            var harmonyParams = patchParams.Where(p => IsHarmonyParameter(p.Name)).ToList();
            var regularParams = patchParams.Where(p => !IsHarmonyParameter(p.Name)).ToList();

            // Validate __instance parameter
            var instanceParam = harmonyParams.FirstOrDefault(p => p.Name == "__instance");
            if (instanceParam != null && !targetMethod.IsStatic)
            {
                var expectedInstanceType = targetMethod.ContainingType;
                if (!SymbolEqualityComparer.Default.Equals(instanceParam.Type, expectedInstanceType))
                {
                    result.AddError($"__instance parameter type '{instanceParam.Type}' doesn't match target method's containing type '{expectedInstanceType}'");
                }
            }
            else if (instanceParam != null && targetMethod.IsStatic)
            {
                result.AddWarning("__instance parameter found but target method is static");
            }

            // Validate __result parameter
            var resultParam = harmonyParams.FirstOrDefault(p => p.Name == "__result");
            if (resultParam != null)
            {
                if (targetMethod.ReturnsVoid)
                {
                    result.AddError("__result parameter found but target method returns void");
                }
                else if (!SymbolEqualityComparer.Default.Equals(resultParam.Type, targetMethod.ReturnType))
                {
                    result.AddError($"__result parameter type '{resultParam.Type}' doesn't match target method's return type '{targetMethod.ReturnType}'");
                }
            }

            // Validate regular parameters match target method parameters
            for (int i = 0; i < regularParams.Count && i < targetParams.Count; i++)
            {
                if (!SymbolEqualityComparer.Default.Equals(regularParams[i].Type, targetParams[i].Type))
                {
                    result.AddError($"Parameter {i + 1} type mismatch: expected '{targetParams[i].Type}', got '{regularParams[i].Type}'");
                }
            }

            // Check for extra parameters
            if (regularParams.Count > targetParams.Count)
            {
                result.AddError($"Too many parameters: target method has {targetParams.Count}, patch method has {regularParams.Count} regular parameters");
            }
        }

        private void ValidateReturnTypeCompatibility(IMethodSymbol targetMethod, IMethodSymbol patchMethod, ValidationResult result)
        {
            var patchType = DeterminePatchType(patchMethod);
            var patchReturnType = patchMethod.ReturnType;

            switch (patchType)
            {
                case PatchType.Prefix:
                    if (!patchReturnType.SpecialType.Equals(SpecialType.System_Boolean) &&
                        !patchReturnType.SpecialType.Equals(SpecialType.System_Void))
                    {
                        result.AddError($"Prefix patches should return bool or void, got '{patchReturnType}'");
                    }
                    break;

                case PatchType.Postfix:
                    // Postfix can return void or match target return type
                    if (!patchReturnType.SpecialType.Equals(SpecialType.System_Void) &&
                        !SymbolEqualityComparer.Default.Equals(patchReturnType, targetMethod.ReturnType))
                    {
                        result.AddWarning($"Postfix return type '{patchReturnType}' doesn't match target return type '{targetMethod.ReturnType}' or void");
                    }
                    break;

                case PatchType.Transpiler:
                    var returnTypeString = patchReturnType.ToDisplayString();
                    if (!returnTypeString.Contains("IEnumerable") || !returnTypeString.Contains("CodeInstruction"))
                    {
                        result.AddError($"Transpiler patches should return IEnumerable<CodeInstruction>, got '{returnTypeString}'");
                    }
                    break;
            }
        }

        private void ValidateAccessibility(IMethodSymbol targetMethod, ValidationResult result)
        {
            if (targetMethod.DeclaredAccessibility == Accessibility.Private)
            {
                result.AddWarning("Patching private methods may require additional Harmony configuration");
            }

            if (targetMethod.IsSealed)
            {
                result.AddWarning("Target method is sealed - patch may not work as expected");
            }
        }

        private static string FormatMethodSignature(IMethodSymbol method)
        {
            var parameters = string.Join(", ", method.Parameters.Select(p => $"{p.Type} {p.Name}"));
            return $"{method.ContainingType}.{method.Name}({parameters})";
        }
    }

    private static PatchType DeterminePatchType(IMethodSymbol methodSymbol)
    {
        var methodName = methodSymbol.Name.ToLower();
        var returnType = methodSymbol.ReturnType.Name;

        if (methodName.Contains("transpiler")) return PatchType.Transpiler;
        if (methodName.Contains("post") || methodName.Contains("after")) return PatchType.Postfix;
        if (methodName.Contains("finalizer")) return PatchType.Finalizer;

        // Default to Prefix, but check return type for hints
        if (returnType == "Void") return PatchType.Postfix;
        if (returnType == "Boolean") return PatchType.Prefix;

        return PatchType.Prefix;
    }

    private static string FormatMethodSignature(IMethodSymbol method)
    {
        var parameters = string.Join(", ", method.Parameters.Select(p => $"{p.Type} {p.Name}"));
        return $"{method.ContainingType}.{method.Name}({parameters})";
    }

    public class ValidationResult
    {
        public List<string> Errors { get; } = new();
        public List<string> Warnings { get; } = new();
        public IMethodSymbol? TargetMethod { get; set; }
        public bool IsValid => !Errors.Any();

        public void AddError(string error) => Errors.Add(error);
        public void AddWarning(string warning) => Warnings.Add(warning);
    }

    private static PatchDefinition AnalyzePatchMethod(IMethodSymbol methodSymbol, MethodDeclarationSyntax methodSyntax, ComprehensiveValidator validator)
    {
        var patchDef = new PatchDefinition
        {
            OriginalMethodName = methodSymbol.Name,
            ContainingClass = methodSymbol.ContainingType.ToDisplayString(), // Use full qualified name
            ReturnType = methodSymbol.ReturnType.ToDisplayString(),
            Parameters = methodSymbol.Parameters.ToList()
        };

        // Extract AutoPatch attribute parameters
        var autoPatchAttr = methodSyntax.AttributeLists
            .SelectMany(al => al.Attributes)
            .FirstOrDefault(attr => attr.Name.ToString().Contains("AutoPatch"));

        if (autoPatchAttr?.ArgumentList?.Arguments.Count > 0)
        {
            // Explicit target specified: [AutoPatch("UnityEngine.GameObject", "SetActive")]
            var args = autoPatchAttr.ArgumentList.Arguments;
            if (args.Count >= 2)
            {
                patchDef.TargetType = ExtractStringFromAttributeArgument(args[0]);
                patchDef.TargetMethod = ExtractStringFromAttributeArgument(args[1]);
            }
            else if (args.Count == 1)
            {
                // Single argument format: [AutoPatch("UnityEngine.GameObject.SetActive")]
                var fullTarget = ExtractStringFromAttributeArgument(args[0]);
                var lastDot = fullTarget.LastIndexOf('.');
                if (lastDot > 0)
                {
                    patchDef.TargetType = fullTarget.Substring(0, lastDot);
                    patchDef.TargetMethod = fullTarget.Substring(lastDot + 1);
                }
            }
        }
        else
        {
            // Auto-detect target from method name and parameters
            AutoDetectTarget(patchDef, methodSymbol);
        }

        // Determine patch type
        patchDef.PatchType = DeterminePatchType(methodSymbol);

        // Comprehensive validation using the new validator
        if (!string.IsNullOrEmpty(patchDef.TargetType) && !string.IsNullOrEmpty(patchDef.TargetMethod))
        {
            var validationResult = validator.ValidateTargetMethod(patchDef.TargetType, patchDef.TargetMethod, methodSymbol);

            if (validationResult.IsValid)
            {
                patchDef.IsValid = true;
                patchDef.TargetSignature = FormatMethodSignature(validationResult.TargetMethod!);
            }
            else
            {
                patchDef.IsValid = false;
                patchDef.ErrorMessage = string.Join("\n", validationResult.Errors);
            }
        }
        else
        {
            patchDef.IsValid = false;
            patchDef.ErrorMessage = "Target type and method must be specified or auto-detectable";
        }

        return patchDef;
    }

    private static void AutoDetectTarget(PatchDefinition patchDef, IMethodSymbol methodSymbol)
    {
        var methodName = methodSymbol.Name;
        
        // Remove patch-related suffixes/prefixes
        var cleanName = methodName
            .Replace("Prefix", "")
            .Replace("Postfix", "")
            .Replace("Pre", "")
            .Replace("Post", "")
            .Replace("Patch", "")
            .Replace("Hook", "")
            .Trim();

        patchDef.TargetMethod = cleanName;

        // Try to detect target type from __instance parameter
        var instanceParam = methodSymbol.Parameters.FirstOrDefault(p => p.Name == "__instance");
        if (instanceParam != null)
        {
            patchDef.TargetType = instanceParam.Type.ToDisplayString();
        }
        else
        {
            // Look for typed parameters that might indicate the target
            var firstParam = methodSymbol.Parameters.FirstOrDefault();
            if (firstParam != null && !IsHarmonyParameter(firstParam.Name))
            {
                patchDef.TargetType = firstParam.Type.ToDisplayString();
            }
        }

        if (string.IsNullOrEmpty(patchDef.TargetType))
        {
            patchDef.IsValid = false;
            patchDef.ErrorMessage = "Cannot auto-detect target type. Please specify explicitly in AutoPatch attribute.";
        }
    }

    private static void ValidatePatch(PatchDefinition patchDef)
    {
        if (string.IsNullOrEmpty(patchDef.TargetType) || string.IsNullOrEmpty(patchDef.TargetMethod))
        {
            patchDef.IsValid = false;
            patchDef.ErrorMessage = "Target type and method must be specified";
            return;
        }

        // Validate based on patch type
        switch (patchDef.PatchType)
        {
            case PatchType.Prefix:
                if (patchDef.ReturnType != "System.Boolean" && patchDef.ReturnType != "void")
                {
                    patchDef.ErrorMessage = "Prefix patches should return bool or void";
                }
                break;
            case PatchType.Postfix:
                // Postfix can return void or modify __result
                break;
            case PatchType.Transpiler:
                if (!patchDef.ReturnType.Contains("IEnumerable"))
                {
                    patchDef.ErrorMessage = "Transpiler patches should return IEnumerable<CodeInstruction>";
                }
                break;
        }

        patchDef.IsValid = string.IsNullOrEmpty(patchDef.ErrorMessage);
        patchDef.TargetSignature = $"{patchDef.TargetType}.{patchDef.TargetMethod}";
    }

    private static string GenerateHarmonyPatches(List<PatchDefinition> patches)
    {
        var sb = new StringBuilder();
        
        sb.AppendLine("// Auto-generated Harmony patches by AutoPatch Source Generator");
        sb.AppendLine("// This file contains the actual HarmonyPatch-decorated methods");
        sb.AppendLine("// Organized by source class for better isolation and maintainability");
        sb.AppendLine();
        sb.AppendLine("using System;");
        sb.AppendLine("using System.Collections.Generic;");
        sb.AppendLine("using System.Reflection;");
        sb.AppendLine("using System.Reflection.Emit;");
        sb.AppendLine("using HarmonyLib;");
        sb.AppendLine("using MelonLoader;");
        
        // Add using statements for user's namespaces to avoid fully qualified names
        var userNamespaces = patches
            .Select(p => GetNamespaceFromFullName(p.ContainingClass))
            .Where(ns => !string.IsNullOrEmpty(ns))
            .Distinct()
            .Where(ns => !IsSystemNamespace(ns));
            
        foreach (var ns in userNamespaces)
        {
            sb.AppendLine($"using {ns};");
        }
        
        sb.AppendLine();

        // Generate patch info as comments
        sb.AppendLine("/*");
        sb.AppendLine("AutoPatch Generation Results:");
        sb.AppendLine($"  Generated {patches.Count} patches with comprehensive validation");
        sb.AppendLine("*/");
        sb.AppendLine();

        sb.AppendLine("namespace AutoPatch.Generated");
        sb.AppendLine("{");

        // Group patches by source class for better organization
        var patchGroups = patches.GroupBy(p => p.ContainingClass).ToList();
        
        foreach (var group in patchGroups)
        {
            var sourceClass = group.Key;
            var classPatches = group.ToList();
            var simpleClassName = GetSimpleClassName(sourceClass);
            
            sb.AppendLine($"    // Generated patches for {sourceClass}");
            sb.AppendLine($"    public static class Generated_{simpleClassName}_Patches");
            sb.AppendLine("    {");
            
            foreach (var patch in classPatches)
            {
                GenerateHarmonyPatchMethod(sb, patch, simpleClassName);
                sb.AppendLine();
            }
            
            sb.AppendLine("    }");
            sb.AppendLine();
        }

        // Generate a main patches class that references all the individual patch classes
        sb.AppendLine("    // Main patches class for easy PatchAll() reference");
        sb.AppendLine("    public static class GeneratedPatches");
        sb.AppendLine("    {");
        sb.AppendLine("        // This class exists for backward compatibility with PatchAll(typeof(GeneratedPatches))");
        sb.AppendLine("        // Individual patch classes are organized above by source class");
        sb.AppendLine("        public static System.Type[] GetAllPatchTypes()");
        sb.AppendLine("        {");
        sb.AppendLine("            return new System.Type[]");
        sb.AppendLine("            {");
        
        foreach (var group in patchGroups)
        {
            var simpleClassName = GetSimpleClassName(group.Key);
            sb.AppendLine($"                typeof(Generated_{simpleClassName}_Patches),");
        }
        
        sb.AppendLine("            };");
        sb.AppendLine("        }");
        sb.AppendLine("    }");
        sb.AppendLine("}");

        return sb.ToString();
    }

    private static void GenerateHarmonyPatchMethod(StringBuilder sb, PatchDefinition patch, string simpleClassName)
    {
        // Generate multiple HarmonyPatch approaches for better type safety
        sb.AppendLine("        // Type-safe patch using MethodInfo (recommended approach)");
        sb.AppendLine($"        [HarmonyPatch]");
        sb.AppendLine("        [HarmonyPatch(MethodType.Normal)]");
        
        // Generate method signature with proper naming: "Initialize_Postfix" not "InitializePostfix_Postfix"
        var cleanMethodName = RemovePatchSuffixFromMethodName(patch.OriginalMethodName);
        var patchMethodName = $"{cleanMethodName}_{patch.PatchType}";
        var harmonyAttributeName = GetHarmonyAttributeName(patch.PatchType);
        sb.AppendLine($"        [{harmonyAttributeName}]");
        sb.AppendLine($"        public static {patch.ReturnType} {patchMethodName}(");
        
        // Generate parameters
        var paramStrings = new List<string>();
        
        foreach (var param in patch.Parameters)
        {
            var paramType = param.Type.ToDisplayString();
            var paramName = param.Name;
            paramStrings.Add($"{paramType} {paramName}");
        }
        
        sb.AppendLine($"            {string.Join(",\n            ", paramStrings)})");
        sb.AppendLine("        {");
        
        // Generate method body - delegate to original method
        sb.AppendLine($"            // Generated patch for {patch.TargetSignature}");
        sb.AppendLine($"            // Delegates to: {patch.ContainingClass}.{patch.OriginalMethodName}");
        sb.AppendLine();
        
        // Call the original user method with simplified class name if using statement added
        var callParams = string.Join(", ", patch.Parameters.Select(p => p.Name));
        
        if (patch.ReturnType == "void")
        {
            sb.AppendLine($"            {simpleClassName}.{patch.OriginalMethodName}({callParams});");
        }
        else
        {
            sb.AppendLine($"            return {simpleClassName}.{patch.OriginalMethodName}({callParams});");
        }
        
        sb.AppendLine("        }");
        sb.AppendLine();
        
        // Generate TargetMethod for type safety
        GenerateTargetMethod(sb, patch, patchMethodName);
    }

    private static string GeneratePatchApplier(List<PatchDefinition> patches)
    {
        var sb = new StringBuilder();
        
        sb.AppendLine("// Auto-generated patch applier");
        sb.AppendLine("using System;");
        sb.AppendLine("using HarmonyLib;");
        sb.AppendLine("using MelonLoader;");
        sb.AppendLine();
        sb.AppendLine("namespace AutoPatch.Generated");
        sb.AppendLine("{");
        sb.AppendLine("    public static class AutoPatchApplier");
        sb.AppendLine("    {");
        sb.AppendLine("        // NOTE: MelonLoader automatically applies [HarmonyPatch] decorated methods!");
        sb.AppendLine("        // This method is provided for manual control if needed, or for logging purposes.");
        sb.AppendLine("        public static void LogPatchInfo()");
        sb.AppendLine("        {");
        sb.AppendLine($"            MelonLogger.Msg(\"[AutoPatch] Generated {patches.Count} auto-patches (auto-applied by MelonLoader):\");");
        sb.AppendLine();
        
        foreach (var patch in patches)
        {
            sb.AppendLine($"            MelonLogger.Msg(\"[AutoPatch]   ✓ {patch.OriginalMethodName} -> {patch.TargetSignature}\");");
        }
        
        sb.AppendLine("        }");
        sb.AppendLine();
        sb.AppendLine("        // Legacy method for manual patching (usually not needed in modern MelonLoader)");
        sb.AppendLine("        public static void ManuallyApplyPatches(HarmonyLib.Harmony harmony = null)");
        sb.AppendLine("        {");
        sb.AppendLine("            if (harmony == null)");
        sb.AppendLine("            {");
        sb.AppendLine("                MelonLogger.Warning(\"[AutoPatch] Manual patching requested but no Harmony instance provided.\");");
        sb.AppendLine("                MelonLogger.Msg(\"[AutoPatch] Note: MelonLoader should auto-apply patches with [HarmonyPatch] attributes.\");");
        sb.AppendLine("                return;");
        sb.AppendLine("            }");
        sb.AppendLine();
        sb.AppendLine("            try");
        sb.AppendLine("            {");
        sb.AppendLine("                // Apply patches from all generated patch classes");
        sb.AppendLine("                foreach (var patchType in GeneratedPatches.GetAllPatchTypes())");
        sb.AppendLine("                {");
        sb.AppendLine("                    harmony.PatchAll(patchType);");
        sb.AppendLine("                }");
        sb.AppendLine($"                MelonLogger.Msg(\"[AutoPatch] ✓ Manually applied all {patches.Count} patches!\");");
        sb.AppendLine("                LogPatchInfo();");
        sb.AppendLine("            }");
        sb.AppendLine("            catch (Exception ex)");
        sb.AppendLine("            {");
        sb.AppendLine("                MelonLogger.Error($\"[AutoPatch] ✗ Failed to manually apply patches: {ex.Message}\");");
        sb.AppendLine("                MelonLogger.Error($\"[AutoPatch] Stack trace: {ex.StackTrace}\");");
        sb.AppendLine("            }");
        sb.AppendLine("        }");
        sb.AppendLine("    }");
        sb.AppendLine("}");
        
        return sb.ToString();
    }

    // Helper methods
    private static string ExtractStringFromAttributeArgument(AttributeArgumentSyntax arg)
    {
        if (arg.Expression is LiteralExpressionSyntax literal)
        {
            return literal.Token.ValueText;
        }
        return arg.Expression.ToString().Trim('"');
    }

    private static bool IsHarmonyParameter(string paramName)
    {
        return paramName.StartsWith("__") ||
               paramName == "instance" ||
               paramName == "result";
    }

    private static string GetHarmonyAttributeName(PatchType patchType)
    {
        return patchType switch
        {
            PatchType.Prefix => "HarmonyPrefix",
            PatchType.Postfix => "HarmonyPostfix",
            PatchType.Transpiler => "HarmonyTranspiler",
            PatchType.Finalizer => "HarmonyFinalizer",
            _ => "HarmonyPrefix"
        };
    }

    private static string GetNamespaceFromFullName(string fullName)
    {
        var lastDot = fullName.LastIndexOf('.');
        return lastDot > 0 ? fullName.Substring(0, lastDot) : string.Empty;
    }

    private static string GetSimpleClassName(string fullName)
    {
        var lastDot = fullName.LastIndexOf('.');
        return lastDot > 0 ? fullName.Substring(lastDot + 1) : fullName;
    }

    /// <summary>
    /// Removes known patch-related suffixes (Prefix, Postfix, Transpiler, Finalizer, Patch, Hook)
    /// from a method name so the generated method doesn’t get double-suffixed.
    /// </summary>
    private static string RemovePatchSuffixFromMethodName(string methodName)
    {
        if (string.IsNullOrEmpty(methodName))
            return methodName;

        var suffixes = new[]
        {
            "Prefix",
            "Postfix",
            "Transpiler",
            "Finalizer",
            "Patch",
            "Hook"
        };

        foreach (var suffix in suffixes)
        {
            if (methodName.EndsWith(suffix, StringComparison.OrdinalIgnoreCase))
            {
                return methodName.Substring(0, methodName.Length - suffix.Length);
            }
        }

        return methodName;
    }


    private static bool IsSystemNamespace(string ns)
    {
        return ns.StartsWith("System") ||
               ns.StartsWith("Microsoft") ||
               ns.StartsWith("UnityEngine") ||
               ns.StartsWith("Il2Cpp");
    }

    private static void GenerateTargetMethod(StringBuilder sb, PatchDefinition patch, string patchMethodName)
    {
        sb.AppendLine($"        // TargetMethod for type-safe method resolution");
        sb.AppendLine($"        public static MethodInfo TargetMethod_{patchMethodName}()");
        sb.AppendLine("        {");
        sb.AppendLine($"            // Use AccessTools for robust method resolution without string literals");
        
        // Generate parameter types array
        if (patch.Parameters.Any(p => !IsHarmonyParameter(p.Name)))
        {
            var nonHarmonyParams = patch.Parameters.Where(p => !IsHarmonyParameter(p.Name)).ToList();
            sb.AppendLine("            var paramTypes = new Type[]");
            sb.AppendLine("            {");
            foreach (var param in nonHarmonyParams)
            {
                sb.AppendLine($"                typeof({param.Type.ToDisplayString()}),");
            }
            sb.AppendLine("            };");
            sb.AppendLine();
            sb.AppendLine($"            var method = AccessTools.Method(typeof({patch.TargetType}), nameof({patch.TargetType}.{patch.TargetMethod}), paramTypes);");
        }
        else
        {
            // No parameters or only Harmony parameters
            sb.AppendLine($"            var method = AccessTools.Method(typeof({patch.TargetType}), nameof({patch.TargetType}.{patch.TargetMethod}));");
        }
        
        sb.AppendLine("            if (method == null)");
        sb.AppendLine("            {");
        sb.AppendLine($"                throw new System.Exception($\"[AutoPatch] Could not find method {{typeof({patch.TargetType}).Name}}.{patch.TargetMethod}\");");
        sb.AppendLine("            }");
        sb.AppendLine("            return method;");
        sb.AppendLine("        }");
    }



    private static void ReportDiagnostic(SourceProductionContext context, MethodDeclarationSyntax method,
        string id, string title, string message, DiagnosticSeverity severity)
    {
        var descriptor = new DiagnosticDescriptor(id, title, message, "AutoPatch", severity, true);
        context.ReportDiagnostic(Diagnostic.Create(descriptor, method.GetLocation()));
    }

    // Data classes
    private class PatchDefinition
    {
        public string OriginalMethodName { get; set; }
        public string ContainingClass { get; set; }
        public string TargetType { get; set; }
        public string TargetMethod { get; set; }
        public string TargetSignature { get; set; }
        public PatchType PatchType { get; set; }
        public string ReturnType { get; set; }
        public List<IParameterSymbol> Parameters { get; set; } = new List<IParameterSymbol>();
        public bool IsValid { get; set; } = true;
        public string ErrorMessage { get; set; }
    }

    private enum PatchType
    {
        Prefix,
        Postfix,
        Transpiler,
        Finalizer
    }
}

// AutoPatch attribute definition (put this in a separate file)
// [System.AttributeUsage(System.AttributeTargets.Method)]
// public class AutoPatchAttribute : System.Attribute
// {
//     public string TargetType { get; set; }
//     public string TargetMethod { get; set; }
//     
//     public AutoPatchAttribute() { }
//     
//     public AutoPatchAttribute(string targetType, string targetMethod)
//     {
//         TargetType = targetType;
//         TargetMethod = targetMethod;
//     }
//     
//     public AutoPatchAttribute(string fullTarget)
//     {
//         var parts = fullTarget.Split('.');
//         if (parts.Length >= 2)
//         {
//             TargetMethod = parts[parts.Length - 1];
//             TargetType = string.Join(".", parts.Take(parts.Length - 1));
//         }
//     }
// }

/*
=== USAGE EXAMPLES ===

// Super simple - auto-detection (patches auto-applied by MelonLoader!)
public static class MyPatches
{
    [AutoPatch] // Auto-detects: GameObject.SetActive (from __instance parameter)
    public static bool SetActivePrefix(UnityEngine.GameObject __instance, bool value)
    {
        MelonLogger.Msg($"Setting {__instance.name} active: {value}");
        return true; // Continue with original
    }

    [AutoPatch] // Auto-detects: postfix for CalculateScore (void return type)
    public static void CalculateScorePostfix(ref int __result)
    {
        __result *= 2; // Double the score!
    }
}

// Explicit targeting
public static class ExplicitPatches
{
    [AutoPatch("UnityEngine.Transform", "Translate")]
    public static bool TranslatePrefix(UnityEngine.Transform __instance, Vector3 translation)
    {
        MelonLogger.Msg($"Translating {__instance.name} by {translation}");
        return true;
    }
    
    [AutoPatch("MyGame.Player.TakeDamage")]
    public static void TakeDamagePostfix(ref float __result, float damage)
    {
        MelonLogger.Msg($"Player took {damage} damage, health now: {__result}");
    }
}

// In your mod's main class (OPTIONAL - patches auto-apply!):
public override void OnApplicationStart()
{
    // Just for logging what patches were generated:
    AutoPatch.Generated.AutoPatchApplier.LogPatchInfo();
    
    // Manual patching only if you need special control:
    // var harmony = new Harmony("com.yourmod.autopatch");
    // AutoPatch.Generated.AutoPatchApplier.ManuallyApplyPatches(harmony);
}
*/